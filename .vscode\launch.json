{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "LiancanConsumeApplication",
            "request": "launch",
            "mainClass": "com.rabbitframework.boot.LiancanConsumeApplication",
            "projectName": "liancan-consume",
            "cwd": "${workspaceFolder}/ruoyi-modules/liancan-consume",
            "console": "integratedTerminal",
            "args": "",
            "vmArgs": [
                "-Dfile.encoding=UTF-8",
                "-Dspring.output.ansi.enabled=ALWAYS",
                // Spring Boot Profiles
                "-Dspring.profiles.active=dev",
                // Spring Cloud Nacos 配置
                "-Dnacos.server=*************:8848",
                "-Dnacos.username=nacos",
                "-Dnacos.password=nacos",
                "-Dspring.cloud.nacos.config.namespace=dev",
                "-Dspring.cloud.nacos.discovery.namespace=dev",
                // Dubbo的注册中心客户端配置
                "-Ddubbo.registry.address=nacos://*************:8848",
                "-Ddubbo.registry.parameters.namespace=dev",
                // 禁用 Spring Cloud Bus (避免 RabbitMQ 连接错误)
                "-Dspring.cloud.bus.enabled=false"
            ]
        },
        {
            "type": "java",
            "name": "LiancanMqApplication",
            "request": "launch",
            "mainClass": "com.rabbitframework.boot.mq.LiancanMqApplication",
            "projectName": "liancan-mq",
            "cwd": "${workspaceFolder}/ruoyi-modules/liancan-mq",
            "console": "integratedTerminal",
            "args": "",
            "vmArgs": [
                "-Dfile.encoding=UTF-8",
                "-Dspring.output.ansi.enabled=ALWAYS",
                // Spring Boot Profiles
                "-Dspring.profiles.active=dev",
                // Spring Cloud Nacos 配置
                "-Dnacos.server=*************:8848",
                "-Dnacos.username=nacos",
                "-Dnacos.password=nacos",
                "-Dspring.cloud.nacos.config.namespace=dev",
                "-Dspring.cloud.nacos.discovery.namespace=dev",
                // Dubbo的注册中心客户端配置
                "-Ddubbo.registry.address=nacos://*************:8848",
                "-Ddubbo.registry.parameters.namespace=dev",
                // 禁用 Spring Cloud Bus (避免 RabbitMQ 连接错误)
                "-Dspring.cloud.bus.enabled=false"
            ]
        },
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "RuoYiAuthApplication",
            "request": "launch",
            "mainClass": "org.dromara.auth.RuoYiAuthApplication",
            "projectName": "ruoyi-auth"
        },
        {
            "type": "java",
            "name": "RuoYiDemoApplication",
            "request": "launch",
            "mainClass": "org.dromara.demo.RuoYiDemoApplication",
            "projectName": "ruoyi-demo"
        },
        {
            "type": "java",
            "name": "RuoYiGatewayApplication",
            "request": "launch",
            "mainClass": "org.dromara.gateway.RuoYiGatewayApplication",
            "projectName": "ruoyi-gateway"
        },
        {
            "type": "java",
            "name": "RuoYiGenApplication",
            "request": "launch",
            "mainClass": "org.dromara.gen.RuoYiGenApplication",
            "projectName": "ruoyi-gen"
        },
        {
            "type": "java",
            "name": "RuoYiJobApplication",
            "request": "launch",
            "mainClass": "org.dromara.job.RuoYiJobApplication",
            "projectName": "ruoyi-job"
        },
        {
            "type": "java",
            "name": "RuoYiMonitorApplication",
            "request": "launch",
            "mainClass": "org.dromara.modules.monitor.RuoYiMonitorApplication",
            "projectName": "ruoyi-monitor"
        },
        {
            "type": "java",
            "name": "Nacos",
            "request": "launch",
            "mainClass": "com.alibaba.nacos.Nacos",
            "projectName": "ruoyi-nacos"
        },
        {
            "type": "java",
            "name": "RuoYiResourceApplication",
            "request": "launch",
            "mainClass": "org.dromara.resource.RuoYiResourceApplication",
            "projectName": "ruoyi-resource"
        },
        {
            "type": "java",
            "name": "SeataServerApplication",
            "request": "launch",
            "mainClass": "org.apache.seata.server.SeataServerApplication",
            "projectName": "ruoyi-seata-server"
        },
        {
            "type": "java",
            "name": "DashboardApplication",
            "request": "launch",
            "mainClass": "com.alibaba.csp.sentinel.dashboard.DashboardApplication",
            "projectName": "ruoyi-sentinel-dashboard"
        },
        {
            "type": "java",
            "name": "SnailJobServerApplication",
            "request": "launch",
            "mainClass": "org.dromara.snailjob.SnailJobServerApplication",
            "projectName": "ruoyi-snailjob-server"
        },
        {
            "type": "java",
            "name": "RuoYiSystemApplication",
            "request": "launch",
            "mainClass": "org.dromara.system.RuoYiSystemApplication",
            "projectName": "ruoyi-system"
        },
        {
            "type": "java",
            "name": "RuoYiTestMqApplication",
            "request": "launch",
            "mainClass": "org.dromara.stream.RuoYiTestMqApplication",
            "projectName": "ruoyi-test-mq"
        },
        {
            "type": "java",
            "name": "RuoYiWorkflowApplication",
            "request": "launch",
            "mainClass": "org.dromara.workflow.RuoYiWorkflowApplication",
            "projectName": "ruoyi-workflow"
        }
    ]
}