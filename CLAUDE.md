# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**RuoYi-Cloud-Plus** is a comprehensive microservices framework built on Spring Cloud Alibaba, extensively enhanced from the original RuoYi-Cloud system with enterprise-grade features including multi-tenant support, workflow engine, distributed monitoring, and extensive microservices capabilities.

## Tech Stack

- **Backend**: Java 17+, Spring Boot 3.4.7, Spring Cloud 2024.0.0
- **Gateway**: Spring Cloud Gateway + LoadBalancer
- **Registry & Config**: Alibaba Nacos 2.4.1 (embedded)
- **RPC**: Apache Dubbo 3.X
- **Auth**: Sa-Token + JWT
- **ORM**: MyBatis-Plus 3.5.12
- **Database**: MySQL 8.0+, Oracle, PostgreSQL, SQLServer
- **Cache**: Redis (Redisson) + Caffeine
- **Message Queue**: RocketMQ, RabbitMQ, Kafka
- **Distributed Transaction**: Seata 2.4.1
- **Workflow**: Warm-Flow 1.7.4, Activiti 5.22.0 (Note: Both are present, may need clarification on usage)
- **Business SDK**: IJPay, WxJava, ZXing
- **ElasticSearch**: Easy-ES 3.0.0
- **Distributed Lock**: Redisson + Lock4j
- **Distributed Job**: SnailJob 1.5.0
- **Monitoring**: 
  - Spring Boot Admin 3.4.7
  - Prometheus + Grafana
  - Apache SkyWalking 9.3.0
- **File Storage**: MinIO + AWS S3 compatible
- **Build**: Maven 3.8+
- **Deployment**: Docker + Docker Compose

## Architecture Structure

```
ruoyi-cloud-plus/
├── ruoyi-auth/                 # Authentication service
├── ruoyi-gateway/             # API Gateway service
├── ruoyi-modules/             # Business modules
│   ├── ruoyi-system/          # Core system services
│   ├── ruoyi-gen/            # Code generator service
│   ├── ruoyi-job/            # Distributed job service
│   ├── ruoyi-resource/       # File management service
│   ├── ruoyi-workflow/       # Workflow service
│   ├── liancan-service/      # 廉餐业务服务
│   ├── liancan-consume/      # 廉餐消费服务
│   ├── liancan-finance/      # 廉餐财务服务
│   ├── liancan-supllier/     # 廉餐供应商服务
├── ruoyi-common/              # Common libraries
│   ├── ruoyi-common-core/    # Core utilities
│   ├── ruoyi-common-redis/   # Redis operations
│   ├── ruoyi-common-security/# Security components
│   ├── liancan-common/       # 廉餐通用模块
│   ├── rabbitframework/      # RabbitMQ框架封装
│   └── ...                   # Other common modules
├── ruoyi-api/                 # API interfaces
├── ruoyi-visual/             # Monitoring & visualization
│   ├── ruoyi-monitor/        # Spring Boot Admin
│   ├── ruoyi-nacos/          # Nacos server
│   ├── ruoyi-seata-server/   # Seata server
│   └── ruoyi-sentinel-dashboard/ # Sentinel dashboard
├── ruoyi-example/            # Demo applications
└── script/                   # Deployment scripts
    ├── docker/               # Docker Compose configurations
    └── config/               # Configuration templates
```

## Key Service Ports

| Service | Port | Description |
|---------|------|-------------|
| Gateway | 8080 | API Gateway |
| Auth | 9210 | Authentication service |
| System | 9201 | Core system services |
| Gen | 9202 | Code generator |
| Job | 9203 | Distributed job service |
| Resource | 9204 | File management |
| Workflow | 9205 | Workflow service |
| Monitor | 9100 | Spring Boot Admin |
| Nacos | 8848 | Configuration center |
| Sentinel | 8718 | Sentinel dashboard |
| Seata | 7091/8091 | Distributed transaction server |
| SnailJob | 8800/17888 | Distributed job server |

## Development Commands

### 🎯 **正确的编译顺序指南**

由于项目模块间复杂的依赖关系，必须按以下顺序进行编译以避免依赖错误：

#### **完整重新编译流程**

1. **清理项目**
```bash
mvn clean -q
```

2. **按层级依赖顺序编译**

**层级1：基础配置模块**
```bash
mvn install -pl ruoyi-api/ruoyi-api-bom,ruoyi-common/ruoyi-common-bom,ruoyi-common/ruoyi-common-alibaba-bom -DskipTests
```

**层级2：核心工具模块**
```bash
mvn install -pl ruoyi-common/ruoyi-common-core,ruoyi-common/ruoyi-common-json,ruoyi-common/ruoyi-common-excel -DskipTests
```

**层级3：API基础模块**
```bash
mvn install -pl ruoyi-api/ruoyi-api-system,ruoyi-api/ruoyi-api-resource,ruoyi-api/ruoyi-api-workflow -DskipTests
```

**层级4：基础设施模块**
```bash
mvn install -pl ruoyi-common/ruoyi-common-bus,ruoyi-common/ruoyi-common-nacos,ruoyi-common/ruoyi-common-sentinel,ruoyi-common/ruoyi-common-redis -DskipTests
```

**层级5：认证安全模块**
```bash
mvn install -pl ruoyi-common/ruoyi-common-satoken,ruoyi-common/ruoyi-common-log,ruoyi-common/ruoyi-common-service-impl -DskipTests
```

**层级6：文档模块**
```bash
mvn install -pl ruoyi-common/ruoyi-common-doc -DskipTests
```

**层级7：廉餐框架模块**
```bash
mvn install -pl ruoyi-common/rabbitframework -DskipTests
```

**层级8：廉餐接口模块**
```bash
mvn install -pl ruoyi-common/liancan-interface -DskipTests
```

**层级9：廉餐API模块**
```bash
mvn install -pl ruoyi-api/ruoyi-api-liancan -DskipTests
```

**层级10：完整项目编译**
```bash
mvn install -DskipTests
```

#### **快速编译命令（当依赖已满足时）**

```bash
# Build all modules (当所有依赖已正确编译时)
mvn clean install -DskipTests

# Build with tests
mvn clean install

# Build specific module with dependencies
mvn clean install -pl ruoyi-modules/ruoyi-system -am

# Build with specific profile
mvn clean install -Pdev

# Build without tests for faster iteration
mvn clean install -DskipTests -T 4
```

#### ⚠️ **已知编译问题及解决方案**

**1. ruoyi-common-sensitive 模块**
- **错误位置**: `target/classes/org/dromara/common/sensitive/annotation/Sensitive.class`
- **问题**: JAR打包时找不到编译的class文件
- **解决方案**: 
  ```bash
  # 单独重新编译该模块
  mvn clean install -pl ruoyi-common/ruoyi-common-sensitive -DskipTests
  ```

**2. ruoyi-system 模块**  
- **错误位置**: `target/classes/org/dromara/system/domain/bo/SysTenantBo.class`
- **问题**: 编译时class文件写入失败，可能是文件被IDE锁定
- **解决方案**: 
  ```bash
  # 关闭IDE，清理target目录，重新编译
  mvn clean
  mvn install -pl ruoyi-modules/ruoyi-system -DskipTests
  ```

**3. ruoyi-workflow 模块**
- **错误位置**: MapStruct生成的映射文件语法错误
- **问题**: `target/generated-sources/annotations/` 下的映射实现类有语法问题  
- **解决方案**: 
  ```bash
  # 清理生成的文件，重新编译
  mvn clean -pl ruoyi-modules/ruoyi-workflow
  mvn compile -pl ruoyi-modules/ruoyi-workflow
  ```

### Development Environment Setup

#### Prerequisites
- Java 17+
- Maven 3.8+
- MySQL 8.0+
- Redis 7+
- Nacos 2.4.1

#### Docker Quick Start
```bash
# Start all required services
cd script/docker
docker-compose up -d mysql nacos redis minio

# Start full environment including monitoring
docker-compose up -d
```

#### Local Development Setup
```bash
# Initialize database
mysql -h localhost -u root -p < sql/ry-cloud.sql
mysql -h localhost -u root -p < sql/ry-config.sql

# Start Nacos (embedded)
# Configure Nacos with application-common.yml in script/config/nacos/

# Run services individually
# In IDEA: Run configurations are provided for each service

# Manual start for debugging
cd ruoyi-auth
mvn spring-boot:run -Dspring.profiles.active=dev

cd ruoyi-gateway  
mvn spring-boot:run -Dspring.profiles.active=dev

cd ruoyi-modules/ruoyi-system
mvn spring-boot:run -Dspring.profiles.active=dev
```

### Testing Commands

```bash
# Run all tests
mvn test

# Run tests for specific module
mvn test -pl ruoyi-modules/ruoyi-system

# Run tests with profile
mvn test -Pdev

# Run specific test class
mvn test -Dtest=SystemUserServiceTest

# Skip tests during build
mvn clean install -DskipTests
```

### Profile Configuration

Available profiles: `dev`, `test`, `prod` (configured in pom.xml)

```yaml
# application-dev.yml example
spring:
  profiles:
    active: dev
  datasource:
    url: *************************************************************************************************************************************************
    username: root
    password: ruoyi123
  redis:
    host: localhost
    port: 6379
    password: 
nacos:
  server-addr: 127.0.0.1:8848
```

## Code Generation

```bash
# Generate code for new tables
# 1. Ensure liancan-service is running
# 2. Navigate to: http://localhost:9201/admin/code/generate
# 3. Configure table with package: com.liancan
# 4. Generate and download code files
# 5. Place generated files in appropriate modules
```

## Module Development Notes

### Adding New Business Module

1. Create new module under `ruoyi-modules/`
2. Copy base structure from `ruoyi-modules/ruoyi-system`
3. Update `pom.xml` with correct artifactId and dependencies
4. Add database tables to `sql/ry-cloud.sql`
5. Register in parent `pom.xml` modules section
6. Configure Nacos for service registration

### 廉餐业务模块 (Special Modules)

The project includes specialized business modules:
- **liancan-service**: Core廉餐 services (port 9201) - 核心业务服务，不包含消费明细功能
- **liancan-consume**: MQ consumption services (port 9203) - 消费明细服务，处理 RocketMQ 消息和完整的 ConsumeDetails 功能
- **liancan-finance**: 财务管理 services  
- **liancan-supplier**: 供应商管理 services

These follow the standard module pattern but include 廉餐-specific business logic and database tables.

### 🚨 **重要架构规则：ConsumeDetails 功能边界**

**ConsumeDetails（消费明细）功能已完全迁移到 liancan-consume 模块**：

#### ✅ **liancan-consume 模块职责**
- 拥有 ConsumeDetails 的完整实现（Controller, Service, Mapper, Entity）
- 处理所有 MQ 消息（MEAL_TAG, FREE_CONSUM_TAG 等）
- 提供 Dubbo 服务给其他模块调用

#### ❌ **liancan-service 模块禁止事项**
- **严禁直接使用** liancan-service 中残留的 ConsumeDetails 相关代码
- **严禁直接调用** `ConsumDetailsService`, `ConsumDetailsMapper` 等本地实现
- **必须通过 Dubbo RPC** 调用 liancan-consume 的 `RemoteConsumDetailsService`

#### 🔄 **正确的调用模式**
```java
// ❌ 错误：在 liancan-service 中直接调用
@Autowired private IConsumDetailsService consumDetailsService;

// ✅ 正确：在 liancan-service 中通过 RPC 调用
@DubboReference private RemoteConsumDetailsService remoteConsumDetailsService;

// ✅ 正确：在 liancan-consume 中直接调用本地服务  
@Autowired private IConsumDetailsService consumDetailsService;
```

## Monitoring & Debugging

### Service Monitoring
- **Spring Boot Admin**: http://localhost:9100 (admin/123456)
- **Nacos Console**: http://localhost:8848/nacos (nacos/nacos)
- **Grafana**: http://localhost:3000 (admin/123456)
- **SkyWalking UI**: http://localhost:18080

### Log Analysis
```bash
# Check service logs
docker logs ruoyi-system

# Real-time log viewing
docker logs -f ruoyi-system

# Check Nacos configuration
curl -X GET "http://localhost:8848/nacos/v1/cs/configs?dataId=ruoyi-system-dev.yml&group=DEFAULT_GROUP"
```

### Common Development Issues

1. **Port conflicts**: Change ports in `application-*.yml`
2. **Database connection**: Verify MySQL is running and tables initialized
3. **Nacos timeout**: Ensure Nacos is accessible from service
4. **Redis connection**: Check Redis is running and accessible
5. **Module dependencies**: Run `mvn clean install -DskipTests` after changes

## Configuration Management

### Nacos Configuration

Configuration in `script/config/nacos/` includes:
- `ruoyi-*.yml` - Service-specific configs
- `application-common.yml` - Shared configuration
- `datasource.yml` - Database configuration

### Environment-Specific Configuration

```yaml
# Use environment profiles
mvn spring-boot:run -Dspring.profiles.active=prod

# Override specific properties
mvn spring-boot:run -Dspring-boot.run.arguments="--server.port=8081"
```

### File Upload Configuration

File storage configuration (MinIO/S3) is in `application-common.yml`:
```yaml
minio:
  url: http://localhost:9000
  username: ruoyi
  password: ruoyi123
  bucket: ruoyi
```

## Testing Strategy

- **Unit Tests**: Located in `src/test/java` of each module
- **Integration Tests**: Use `@SpringBootTest` with embedded database
- **Profile-specific Tests**: Use `@ActiveProfiles("test")`
- **Test Data**: Use `test/resources/application-test.yml` for test configuration

## Deployment

### Docker Deployment
```bash
# Build Docker images for all modules
mvn clean package -DskipTests
docker build -t ruoyi-ruoyi-system:2.4.1 ruoyi-modules/ruoyi-system/

# Deploy using docker-compose
cd script/docker
docker-compose up -d
```

### Production Deployment
- Use `prod` profile with production configuration
- Configure external MySQL/Redis clusters
- Set up Nacos in cluster mode
- Configure service discovery endpoints
- Enable distributed logging (ELK stack)