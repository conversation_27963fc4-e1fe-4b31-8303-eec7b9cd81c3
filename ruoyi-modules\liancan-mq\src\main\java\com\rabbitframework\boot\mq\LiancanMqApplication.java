package com.rabbitframework.boot.mq;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 廉餐消息队列模块
 * 专门处理阿里云RocketMQ的消息发送和监听功能
 * 
 * <AUTHOR>
 */
@EnableDubbo
@SpringBootApplication
public class LiancanMqApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(LiancanMqApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  廉餐消息队列模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }

}